import { getUserCenter } from '@/api/user'
import { TOKEN_KEY } from '@/enums/cacheEnums'
import cache from '@/utils/cache'
import { defineStore } from 'pinia'

interface UserSate {
  userInfo: Record<string, any>
  token: string | null
  temToken: string | null
  selectedIdentity: Record<string, any> | null
}
export const useUserStore = defineStore<any>('userStore', {
  // id: 'userStore',
  state: (): UserSate => ({
    userInfo: {
      sysUser: {
        avatar: '',
        nickname: '',
        username: '',
        phone: '',
        wxOpenid: ''
      },
      sysIdentityList: []
    },
    token: cache.get(TOKEN_KEY) || null,
    temToken: null,
    selectedIdentity: null
  }),
  getters: {
    isLogin: (state: any) => !!state.token
  },
  actions: {
    async getUser() {
      // 获取用户信息
      const { data } = await getUserCenter({})
      // @ts-ignore
      this.userInfo = {
        permissions: [],
        roles: [],
        selfAndChildTenantList: [],
        tenantInfos: [],
        sysUser: data?.sysUser || {},
        sysIdentityList: data?.sysIdentityList || []
      }
    },
    setIdentity(identity: { id: string | number; identityName?: string; identityCode?: string } | null) {
      // 选择身份，缓存id用于请求头
      // @ts-ignore
      this.selectedIdentity = identity
      if (identity && (identity as any).id) {
        cache.set('identityId', (identity as any).id)
      } else {
        cache.remove('identityId')
      }
    },
    login(token: string) {
      // @ts-ignore
      this.token = token
      cache.set(TOKEN_KEY, token)
    },
    logout() {
      // @ts-ignore
      this.token = ''
      // @ts-ignore
      this.userInfo = {}
      // 清除已选身份
      // @ts-ignore
      this.selectedIdentity = null
      cache.remove(TOKEN_KEY)
      cache.remove('identityId')
    }
  },
  // @ts-ignore
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    }
  }
})
