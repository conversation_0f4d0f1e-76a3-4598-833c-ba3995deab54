import request from '@/utils/request'

/**
 * 公告/邀请接口（列表）
 */
export function getInviteAndNotice(data?: any) {
  return request.post({
    url: '/admin/applet/inviteAndNotice',
    data
  })
}

/**
 * 公告/邀请详情
 * /admin/srmTenderNotice/detail/${id}
 */
export function getTenderNoticeDetail(id: string | number) {
  return request.post({
    url: `/admin/srmTenderNotice/detail/${id}`
  })
}

/**
 * 中标公示接口
 */
export function getPublicityPage(data?: any) {
  return request.post({
    url: '/admin/noticeTogether/publicityPage',
    data
  })
}

/**
 * 中标公告接口
 */
export function getPublicNoticePage(data?: any) {
  return request.post({
    url: '/admin/noticeTogether/publicNoticePage',
    data
  })
}

/**
 * 招募公告接口
 */
export function getNoticePage(data?: any) {
  return request.post({
    url: '/admin/noticeTogether/noticePage',
    data
  })
}

// 公告数据类型定义
export interface NoticeItem {
  noticeTitle: string // 标题
  noticeContent: string // 公告内容
  deptName: string // 部门名称
  publishTime: string // 发布时间
  id?: string | number // ID（可选）
  [key: string]: any // 其他可能的字段
}

export interface NoticeListResponse {
  code: number
  data: {
    records: NoticeItem[]
    total: number
    size: number
    current: number
    pages: number
  }
  message: string
}
