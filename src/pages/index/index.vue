<template>
  <view
    class="home-page-meta"
    background-color="#F0F2F5"
    :page-style="pageStyle"
  >
    <view class="home-wrapper">
      <!-- 简化版本，先去掉可能有问题的组件 -->
      <view v-if="tokenExpired" class="token-expired-simple">
        <text>登录已过期，请重新登录</text>
      </view>
      <block v-else>
        <!-- 背景渐变 -->
        <view class="home-bg"></view>

        <!-- 导航栏 -->
        <view class="home-navbar">
          <view
            class="home-navbar-space"
            :style="{ height: (systemStore?.menuButtonInfo?.top || 24) + 'px' }"
          ></view>
          <view class="home-navbar-content">
            <view class="home-navbar-left">
              <image
                class="home-navbar-logo"
                src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/ayn-logo-nav.svg"
                mode="aspectFit"
              />
              <view class="home-navbar-line"></view>
              <text class="home-navbar-title">爱养牛招采平台</text>
            </view>
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="home-search">
          <view class="home-search-input">
            <image class="home-search-icon" src="/static/images/icon-search.svg" />
            <input
              class="home-search-text"
              placeholder="请输入关键词搜索"
              placeholder-style="color: rgba(0, 0, 0, 0.45); font-size: 16px;"
              v-model="searchKeyword"
              @confirm="handleSearch"
            />
            <text class="home-search-btn" @tap="handleSearch">搜索</text>
          </view>
        </view>

        <!-- Banner -->
        <view class="home-banner">
          <home-banner />
        </view>

        <!-- 功能图标区 -->
        <view class="home-icons">
          <view class="home-icon-item" @tap="navigateTo('/pages/procurement-plan/index')">
            <view class="home-icon-bg">
              <view class="home-icon-group procurement-plan">
                <image class="procurement-plan-icon1" src="/static/images/procurement-plan-icon1.svg" />
              </view>
            </view>
            <text class="home-icon-text">采购计划</text>
          </view>
          <view class="home-icon-item" @tap="navigateTo('/pages/procurement-project/index')">
            <view class="home-icon-bg">
              <view class="home-icon-group procurement-project">
                <image class="procurement-project-icon1" src="/static/images/procurement-project-icon1.svg" />
              </view>
            </view>
            <text class="home-icon-text">采购立项</text>
          </view>
          <view class="home-icon-item" @tap="navigateTo('/pages/silage-inquiry/index')">
            <view class="home-icon-bg">
              <image class="home-icon silage-inquiry" src="/static/images/silage-inquiry-icon.svg" />
            </view>
            <text class="home-icon-text">青贮询价</text>
          </view>
        </view>

        <!-- 标签页和公告列表容器 -->
        <view class="home-content-card">
          <!-- 标签页 -->
          <view class="home-tabs">
            <view
              class="home-tab-item"
              :class="{ active: activeTab === 'announcement' }"
              @tap="switchTab('announcement')"
            >
              公告/邀请
            </view>
            <view
              class="home-tab-item"
              :class="{ active: activeTab === 'result' }"
              @tap="switchTab('result')"
            >
              中标公示
            </view>
            <view
              class="home-tab-item"
              :class="{ active: activeTab === 'notice' }"
              @tap="switchTab('notice')"
            >
              中标公告
            </view>
            <view
              class="home-tab-item"
              :class="{ active: activeTab === 'change' }"
              @tap="switchTab('change')"
            >
              招募公告
            </view>
            <view class="home-tab-indicator" :style="tabIndicatorStyle"></view>
          </view>

          <!-- 公告列表 -->
          <view class="home-announcements">
            <!-- 加载中状态 -->
            <view v-if="loading && noticeList.length === 0" class="loading-simple">
              <text>加载中...</text>
            </view>
            <!-- 空数据状态 -->
            <view v-else-if="isEmpty" class="empty-simple">
              <text>暂无数据</text>
            </view>
            <!-- 公告列表 -->
            <view v-else>
              <view
                v-for="(item, index) in noticeList"
                :key="index"
                class="announcement-card"
                @tap="navigateToDetail(activeTab, item)"
              >
                <view class="announcement-header">
                  <view class="announcement-title-row">
                    <view class="announcement-tag" :class="getTagClass(activeTab)">
                      {{ getTagText(activeTab) }}
                    </view>
                    <text class="announcement-title">{{ item.noticeTitle || item.publicityTitle }}</text>
                  </view>
                </view>
                <text class="announcement-content">{{ getContentPreview(item.noticeContent) }}</text>
                <view class="announcement-footer">
                  <view class="announcement-company">{{ item.deptName }}</view>
                  <text class="announcement-time">{{ formatTime(item.publishTime) }}</text>
                </view>
              </view>

              <!-- 加载更多提示 -->
              <view v-if="loading && noticeList.length > 0" class="loading-more">
                <text>加载中...</text>
              </view>
              <view v-else-if="!hasMore && noticeList.length > 0" class="no-more">
                <text>没有更多数据了</text>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
    <tabbar />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useSystemStore } from "@/stores/system";
import { useUserStore } from "@/stores/user";
import HomeBanner from '@/components/home-banner/index.vue'
import {
  onPullDownRefresh,
  onReachBottom,
  onShow,
  onLoad,
} from "@dcloudio/uni-app";
import {
  getInviteAndNotice,
  getPublicityPage,
  getPublicNoticePage,
  getNoticePage,
  type NoticeItem
} from '@/api/notice';

// 状态管理
const systemStore = useSystemStore();
const userStore = useUserStore();
const loading = ref(false);
const pageOptions = ref({})

// 搜索相关
const searchKeyword = ref('')

// 标签页相关
const activeTab = ref('announcement')

// 公告数据
const noticeList = ref<NoticeItem[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMore = ref(true)

// 计算属性
const tokenExpired = computed(() => {
  return userStore?.tokenExpired
})

const pageStyle = computed(() => {
  return ''
})

const isEmpty = computed(() => {
  return noticeList.value.length === 0 && !loading.value
})

// 标签页指示器样式
const tabIndicatorStyle = computed(() => {
  const tabs = ['announcement', 'result', 'notice', 'change']
  const tabIndex = tabs.indexOf(activeTab.value)

  // 简化计算：每个tab占据的空间
  const tabPositions = [9, 34, 59, 84] // 根据实际布局调整的位置百分比

  return {
    left: `${tabPositions[tabIndex]}%`,
    width: '24px'
  }
})

// 获取内容预览
const getContentPreview = (value) => {
  if (!value) return ''

  // 移除HTML标签和&nbsp;实体，只显示纯文本预览
  const textContent = value.replace(/<[^>]*>|&nbsp;/g, '')

  return textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent
}

// 方法
function handleSearch() {
  if (searchKeyword.value.trim()) {
    console.log('搜索关键词:', searchKeyword.value)
    // 这里可以调用搜索API
  }
}

function switchTab(tab: string) {
  activeTab.value = tab
  console.log('切换到标签页:', tab)
  // 重置数据并加载对应标签页的数据
  resetData()
  loadNoticeData()
}

function navigateTo(url: string) {
  uni.navigateTo({
    url
  })
}

function navigateToDetail(type: string, item: any) {
  // 构建URL参数，传递基本信息
  const title = encodeURIComponent(item.noticeTitle || item.publicityTitle || '')
  const deptName = encodeURIComponent(item.deptName || '')
  const publishTime = encodeURIComponent(item.publishTime || '')

  uni.navigateTo({
    url: `/pages/announcement-detail/index?type=${type}&id=${item.id}&title=${title}&deptName=${deptName}&publishTime=${publishTime}`
  })
}

// 重置数据
function resetData() {
  noticeList.value = []
  currentPage.value = 1
  total.value = 0
  hasMore.value = true
}

// 根据当前标签页加载对应的公告数据
async function loadNoticeData(isLoadMore = false) {
  if (loading.value) return

  try {
    loading.value = true

    // 如果不是加载更多，重置页码
    if (!isLoadMore) {
      currentPage.value = 1
    }

    const params = {
      current: currentPage.value,
      size: pageSize.value
    }

    let apiFunction
    switch (activeTab.value) {
      case 'announcement':
        apiFunction = getInviteAndNotice
        break
      case 'result':
        apiFunction = getPublicityPage
        break
      case 'notice':
        apiFunction = getPublicNoticePage
        break
      case 'change':
        apiFunction = getNoticePage
        break
      default:
        apiFunction = getInviteAndNotice
    }

    const response = await apiFunction(params)
    console.log(response)

    const { records, total: totalCount, current, pages } = response.data

    if (isLoadMore) {
      // 加载更多时追加数据
      noticeList.value = [...noticeList.value, ...records]
    } else {
      // 首次加载或刷新时替换数据
      noticeList.value = records
    }

    total.value = totalCount
    currentPage.value = current
    hasMore.value = current < pages
  } catch (error) {
    console.error('加载公告数据出错:', error)
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载更多数据
function loadMoreData() {
  if (hasMore.value && !loading.value) {
    currentPage.value += 1
    loadNoticeData(true)
  }
}

// 获取标签样式类名
function getTagClass(tab: string) {
  const classMap: Record<string, string> = {
    'announcement': 'inquiry',
    'result': 'tender',
    'notice': 'negotiation',
    'change': 'recruitment'
  }
  return classMap[tab] || 'inquiry'
}

// 获取标签文本
function getTagText(tab: string) {
  const textMap: Record<string, string> = {
    'announcement': '公告/邀请',
    'result': '中标公示',
    'notice': '中标公告',
    'change': '招募公告'
  }
  return textMap[tab] || '公告'
}

// 格式化时间
function formatTime(timeStr: string) {
  if (!timeStr) return ''

  try {
    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${year}/${month}/${day} ${hours}:${minutes}`
  } catch (error) {
    console.error('时间格式化错误:', error)
    return timeStr
  }
}

// 生命周期
onShow(async () => {
  if (!tokenExpired.value) {
    // 加载首页数据
    await loadNoticeData()
  }
})

onLoad((options) => {
  pageOptions.value = options
  // 初始化系统信息
  try {
    systemStore.initSystemInfo()
  } catch (error) {
    console.warn('初始化系统信息失败:', error)
  }
})

onPullDownRefresh(async () => {
  console.log("onPullDownRefresh");
  if (!tokenExpired.value) {
    // 刷新数据
    await loadNoticeData()
    uni.stopPullDownRefresh()
  }
});

onReachBottom(() => {
  console.log("onReachBottom");
  if (!tokenExpired.value) {
    // 加载更多数据
    loadMoreData()
  }
});
</script>

<style lang="scss">
.home-page-meta {
  min-height: 100vh;
  background: #F0F2F5;
}

.home-wrapper {
  background: #F0F2F5;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.home-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 461px;
  background: linear-gradient(180deg, #A0C5FF 0%, rgba(160, 197, 255, 0) 100%);
  z-index: 1;
}

// 导航栏
.home-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: linear-gradient(180deg, #A0C5FF 0%, rgba(160, 197, 255, 0) 100%);
  //background-color: transparent;

  &-space {
    width: 100%;
  }

  &-content {
    padding: 0 12px 12px;
  }

  &-left {
    display: flex;
    align-items: center;
    height: 30px;
  }

  &-logo {
    width: 92px;
    height: 30px;
  }

  &-line {
    width: 0.5px;
    height: 16px;
    background-color: rgba(0, 0, 0, 0.2);
    margin: 0 12px;
  }

  &-title {
    color: #1C2026;
    font-family: 'PingFang SC';
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
  }
}

// 搜索框
.home-search {
  position: relative;
  z-index: 10;
  padding: 0 12px;
  margin-top: 102px;

  &-input {
    display: flex;
    align-items: center;
    height: 44px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 22px;
    padding: 0 18px;
    gap: 12px;
  }

  &-icon {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }

  &-text {
    flex: 1;
    height: 24px;
    font-size: 16px;
    color: #1D2129;
    background: transparent;
    border: none;
    outline: none;
  }

  &-btn {
    color: #0069FF;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }
}

// Banner
.home-banner {
  position: relative;
  z-index: 10;
  padding: 12px;
}

// 功能图标区
.home-icons {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-around;
  padding: 10px 0;
  margin: 0 12px 12px 12px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.25);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.50) 100%);
}

.home-icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.home-icon-bg {
  width: 32px;
  height: 32px;
  //background: #FFFFFF;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-icon-group {
  &.procurement-plan {
    .procurement-plan-icon1 {
      width: 32px;
      height: 32px;
    }
  }

  &.procurement-project {
    .procurement-project-icon1 {
      width: 32px;
      height: 32px;
    }
  }
}

.home-icon {
  &.silage-inquiry {
    width: 32px;
    height: 32px;
  }
}

.home-icon-text {
  color: #1D2129;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  text-align: center;
}

// 内容卡片容器
.home-content-card {
  position: relative;
  z-index: 10;
  overflow: hidden;
  border-radius: 16px 16px 0 0;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.00) 0%, rgba(255, 255, 255, 0.60) 100%);
}

// 标签页
.home-tabs {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-radius: 16px 16px 0 0;
  gap: 16px;
}

.home-tab-item {
  color: #505762;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  text-align: center;
  flex: 1;
  position: relative;

  &.active {
    color: #1C2026;
    font-weight: 500;
  }
}

.home-tab-indicator {
  position: absolute;
  bottom: 0;
  height: 4px;
  background: #1677FF;
  border-radius: 2px 2px 0 0;
  transition: left 0.3s ease;
}

// 公告列表
.home-announcements {
  position: relative;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  //background: #F5F7FA;
}

.announcement-card {
  background: #FFFFFF;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 12px;
}

.announcement-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.announcement-title-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

.announcement-tag {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 4px;
  height: 20px;
  border-radius: 2px;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  white-space: nowrap;

  &.inquiry {
    background: #F0EDF8;
    color: #563F96;
  }

  &.tender {
    background: #FEEAF5;
    color: #C4277E;
  }

  &.negotiation {
    background: #E8F3F5;
    color: #126A7A;
  }

  &.recruitment {
    background: #FFF7E6;
    color: #D46B08;
  }
}

.announcement-title {
  color: #1D2129;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  flex: 1;
}

.announcement-content {
  color: #4E5969;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  // 添加文本溢出控制属性
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.announcement-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.announcement-company {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 4px;
  background: #F5F7FA;
  border-radius: 2px;
  color: #4E5969;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.announcement-time {
  color: #86909C;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
}

// 简化版样式
.token-expired-simple {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  color: #999;
  font-size: 16px;
}

.empty-simple {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}

.loading-simple {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #666;
  font-size: 14px;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  color: #666;
  font-size: 12px;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  color: #999;
  font-size: 12px;
}

</style>
