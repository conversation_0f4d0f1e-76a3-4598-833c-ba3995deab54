<template>
  <view class="api-test-page">
    <view class="header">
      <text class="title">API 接口测试</text>
    </view>
    
    <view class="test-section">
      <view class="section-title">公告接口测试</view>
      
      <view class="api-item">
        <button class="test-btn" @tap="testInviteAndNotice">测试公告/邀请接口</button>
        <view class="result" v-if="results.inviteAndNotice">
          <text class="result-title">结果:</text>
          <text class="result-content">{{ JSON.stringify(results.inviteAndNotice, null, 2) }}</text>
        </view>
      </view>
      
      <view class="api-item">
        <button class="test-btn" @tap="testPublicityPage">测试中标公示接口</button>
        <view class="result" v-if="results.publicityPage">
          <text class="result-title">结果:</text>
          <text class="result-content">{{ JSON.stringify(results.publicityPage, null, 2) }}</text>
        </view>
      </view>
      
      <view class="api-item">
        <button class="test-btn" @tap="testPublicNoticePage">测试中标公告接口</button>
        <view class="result" v-if="results.publicNoticePage">
          <text class="result-title">结果:</text>
          <text class="result-content">{{ JSON.stringify(results.publicNoticePage, null, 2) }}</text>
        </view>
      </view>
      
      <view class="api-item">
        <button class="test-btn" @tap="testNoticePage">测试招募公告接口</button>
        <view class="result" v-if="results.noticePage">
          <text class="result-title">结果:</text>
          <text class="result-content">{{ JSON.stringify(results.noticePage, null, 2) }}</text>
        </view>
      </view>
    </view>
    
    <view class="loading" v-if="loading">
      <text>请求中...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  getInviteAndNotice, 
  getPublicityPage, 
  getPublicNoticePage, 
  getNoticePage 
} from '@/api/notice'

const loading = ref(false)
const results = ref({
  inviteAndNotice: null,
  publicityPage: null,
  publicNoticePage: null,
  noticePage: null
})

// 测试公告/邀请接口
async function testInviteAndNotice() {
  loading.value = true
  try {
    const response = await getInviteAndNotice({
      current: 1,
      size: 10
    })
    results.value.inviteAndNotice = response
    console.log('公告/邀请接口响应:', response)
  } catch (error) {
    console.error('公告/邀请接口错误:', error)
    results.value.inviteAndNotice = { error: error.message || '请求失败' }
  } finally {
    loading.value = false
  }
}

// 测试中标公示接口
async function testPublicityPage() {
  loading.value = true
  try {
    const response = await getPublicityPage({
      current: 1,
      size: 10
    })
    results.value.publicityPage = response
    console.log('中标公示接口响应:', response)
  } catch (error) {
    console.error('中标公示接口错误:', error)
    results.value.publicityPage = { error: error.message || '请求失败' }
  } finally {
    loading.value = false
  }
}

// 测试中标公告接口
async function testPublicNoticePage() {
  loading.value = true
  try {
    const response = await getPublicNoticePage({
      current: 1,
      size: 10
    })
    results.value.publicNoticePage = response
    console.log('中标公告接口响应:', response)
  } catch (error) {
    console.error('中标公告接口错误:', error)
    results.value.publicNoticePage = { error: error.message || '请求失败' }
  } finally {
    loading.value = false
  }
}

// 测试招募公告接口
async function testNoticePage() {
  loading.value = true
  try {
    const response = await getNoticePage({
      current: 1,
      size: 10
    })
    results.value.noticePage = response
    console.log('招募公告接口响应:', response)
  } catch (error) {
    console.error('招募公告接口错误:', error)
    results.value.noticePage = { error: error.message || '请求失败' }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.api-test-page {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  
  .title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}

.test-section {
  background: white;
  border-radius: 10px;
  padding: 20px;
  
  .section-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
  }
}

.api-item {
  margin-bottom: 30px;
  
  .test-btn {
    width: 100%;
    height: 44px;
    background: #007AFF;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    margin-bottom: 15px;
  }
  
  .result {
    background: #f8f8f8;
    border-radius: 6px;
    padding: 15px;
    
    .result-title {
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 10px;
    }
    
    .result-content {
      font-family: monospace;
      font-size: 12px;
      color: #666;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 20px;
  border-radius: 6px;
  z-index: 1000;
}
</style>
