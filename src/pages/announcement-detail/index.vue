<template>
  <view class="announcement-detail-page">
    <!-- 自定义导航栏 -->
    <uv-navbar
      fixed
      placeholder
      :safeAreaInsetTop="true"
      :autoBack="true"
      title="公告详情"
      bgColor="#FFFFFF"
      :titleStyle="{ color: '#1C2026', fontSize: '18px', fontWeight: '600' }"
      leftIconColor="#1C2026"
    />

    <view class="container">
      <!-- 标题与标签 -->
      <view class="header">
        <view
          class="tag"
          :class="getTagClass(type)"
        >
          {{ getTagText(type) }}
        </view>
        <view class="title">{{ detail.noticeTitle || detail.publicityTitle || detail.title || '-' }}</view>
      </view>

      <!-- 元信息 -->
      <view class="meta">
        <view
          class="company"
          v-if="detail.deptName"
        >
          {{ detail.deptName }}
        </view>
        <view
          class="time"
          v-if="detail.publishTime"
        >
          {{ formatTime(detail.publishTime) }}
        </view>
      </view>

      <!-- 富文本内容 -->
      <view
        class="content"
        v-if="contentHtml"
      >
        <uv-parse
          :content="contentHtml"
          :selectable="true"
          :lazyLoad="true"
          :domain="domain"
          :tagStyle="parseTagStyle"
        />
      </view>
      <view
        v-else
        class="empty"
      >
        暂无内容
      </view>

      <!-- 附件列表 -->
      <view
        v-if="attachments.length"
        class="attachments"
      >
        <view class="attach-title">附件</view>
        <view class="attach-list">
          <view
            v-for="(f, i) in attachments"
            :key="i"
            class="attach-item"
            @tap="openAttachment(f)"
          >
            <uv-icon
              name="file-text"
              color="#1677FF"
              size="18"
            />
            <text class="name">{{ f.fileName }}</text>
            <uv-icon
              name="arrow-right"
              color="#C0C4CC"
              size="16"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 统一富文本样式，接近设计稿排版
const parseTagStyle = {
  p: 'margin: 8px 0; line-height: 1.8; color: #1D2129; font-size: 14px;',
  h1: 'margin: 12px 0 8px; font-size: 20px; font-weight: 600; color: #1D2129;',
  h2: 'margin: 12px 0 8px; font-size: 18px; font-weight: 600; color: #1D2129;',
  h3: 'margin: 12px 0 8px; font-size: 16px; font-weight: 600; color: #1D2129;',
  img: 'max-width: 100%; border-radius: 6px;',
  a: 'color: #1677FF;',
  ul: 'padding-left: 22px;',
  ol: 'padding-left: 22px;',
  table: 'width: 100%; border-collapse: collapse; font-size: 13px;',
  th: 'background:#F7F8FA; font-weight: 600; border: 1px solid #F0F0F0; padding: 6px;',
  td: 'border: 1px solid #F0F0F0; padding: 6px;'
} as Record<string, string>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getTenderNoticeDetail } from '@/api/notice'

const type = ref<string>('announcement')
const id = ref<string | number | undefined>(undefined)
const detail = ref<Record<string, any>>({})
const attachments = ref<Array<{ fileName: string; filePath: string }>>([])
const domain = import.meta.env.VITE_APP_BASE_URL

const contentHtml = computed(() => {
  return detail.value.noticeContent || detail.value.publicityContent || detail.value.content || ''
})

function getTagClass(tab: string) {
  const map: Record<string, string> = {
    announcement: 'inquiry',
    result: 'tender',
    notice: 'negotiation',
    change: 'recruitment'
  }
  return map[tab] || 'inquiry'
}

function getTagText(tab: string) {
  const map: Record<string, string> = {
    announcement: '公告/邀请',
    result: '中标公示',
    notice: '中标公告',
    change: '招募公告'
  }
  return map[tab] || '公告'
}

function formatTime(timeStr: string) {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${year}/${month}/${day} ${hours}:${minutes}`
  } catch (e) {
    return timeStr
  }
}

function openAttachment(file: { fileName: string; filePath: string }) {
  const url = file.filePath
  if (!url) return
  // H5/小程序统一用内置 webview 打开
  uni.navigateTo({
    url: `/pages/webview/webview?url=${encodeURIComponent(url)}`
  })
}

async function fetchDetailIfNeeded() {
  if (type.value !== 'announcement' || !id.value) return
  try {
    const { data } = await getTenderNoticeDetail(id.value)
    if (data) {
      // 仅使用富文本与附件覆盖，其他字段保留列表传入的 detail
      detail.value = { ...detail.value, ...data }
      attachments.value = Array.isArray(data.attachmentInfos) ? data.attachmentInfos : []
    }
  } catch (e) {
    console.error('获取公告详情失败', e)
  }
}

onLoad((options: any) => {
  type.value = options?.type || 'announcement'
  id.value = options?.id

  try {
    const eventChannel = getOpenerEventChannel && getOpenerEventChannel()
    eventChannel?.on?.('noticeItem', (item: any) => {
      // 列表带过来的：标题/dept/publishTime 等
      detail.value = item || {}
      // 进入后再拉取详情覆盖富文本和附件
      fetchDetailIfNeeded()
    })
  } catch (e) {
    // 若无法通过 eventChannel 获取，也尝试直接按 id 拉取
    fetchDetailIfNeeded()
  }
})
</script>

<style lang="scss" scoped>
.announcement-detail-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.container {
  padding: 12px 16px 24px;
}

.header {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.tag {
  display: inline-flex;
  align-items: center;
  height: 22px;
  padding: 0 6px;
  border-radius: 2px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;

  &.inquiry {
    background: #f0edf8;
    color: #563f96;
  }
  &.tender {
    background: #feeaf5;
    color: #c4277e;
  }
  &.negotiation {
    background: #e8f3f5;
    color: #126a7a;
  }

  &.recruitment {
    background: #fff7e6;
    color: #d46b08;
  }
}

.title {
  flex: 1;
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  line-height: 26px;
}

.meta {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #86909c;
  font-size: 12px;
}

.company {
  background: #f5f7fa;
  color: #4e5969;
  padding: 0 6px;
  border-radius: 2px;
  line-height: 20px;
}

.time {
  line-height: 20px;
}

.attachments {
  margin-top: 16px;
  background: #fff;
  border-radius: 8px;
  padding: 8px 8px 0 8px;
  .attach-title {
    font-size: 14px;
    color: #1d2129;
    font-weight: 500;
    padding: 8px;
  }
  .attach-list {
    .attach-item {
      display: flex;
      align-items: center;
      padding: 10px 8px;
      border-top: 1px solid #f0f0f0;
      .name {
        flex: 1;
        margin: 0 6px;
        color: #1677ff;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
.content {
  margin-top: 12px;
  background: #fff;
  border-radius: 8px;
  padding: 12px;
}

.empty {
  margin-top: 24px;
  color: #999;
  text-align: center;
}
</style>
