<template>
  <view class="announcement-detail-page">
    <!-- 自定义导航栏 -->
    <uv-navbar
      fixed
      placeholder
      :safeAreaInsetTop="true"
      :autoBack="true"
      title="详情"
      bgColor="#FFFFFF"
      :titleStyle="{ color: '#1C2026', fontSize: '18px', fontWeight: '600' }"
      leftIconColor="#1C2026"
    />

    <view class="container">
      <!-- 公告卡片 -->
      <view class="announcement-card">
        <!-- 标题与标签 -->
        <view class="header">
          <view class="title-row">
            <view
              class="tag"
              :class="getTagClass(type)"
            >
              {{ getTagText(type) }}
            </view>
            <view class="title">{{ detail.noticeTitle || detail.publicityTitle || detail.title || '-' }}</view>
          </view>
        </view>

        <!-- 元信息 -->
        <view class="meta">
          <view
            class="company"
            v-if="companyName"
          >
            {{ companyName }}
          </view>
          <view
            class="time"
            v-if="publishTime"
          >
            {{ formatTime(publishTime) }}
          </view>
        </view>
      </view>

      <!-- 富文本内容卡片 -->
      <view
        class="content-card"
        v-if="contentHtml"
      >
        <uv-parse
          :content="contentHtml"
          :selectable="true"
          :lazyLoad="true"
          :domain="domain"
          :tagStyle="parseTagStyle"
        />
      </view>
      <view
        v-else
        class="empty"
      >
        暂无内容
      </view>

      <!-- 附件列表 -->
      <view
        v-if="attachments.length"
        class="attachments"
      >
        <view class="attach-title">附件</view>
        <view class="attach-list">
          <view
            v-for="(f, i) in attachments"
            :key="i"
            class="attach-item"
            @tap="openAttachment(f)"
          >
            <uv-icon
              name="file-text"
              color="#1677FF"
              size="18"
            />
            <text class="name">{{ f.fileName }}</text>
            <uv-icon
              name="arrow-right"
              color="#C0C4CC"
              size="16"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <view
        class="btn btn-outline"
        @tap="viewAttachments"
      >
        查看附件
      </view>
      <view
        class="btn btn-primary"
        @tap="goHome"
      >
        返回首页
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 统一富文本样式，接近设计稿排版
const parseTagStyle = {
  p: 'margin: 8px 0; line-height: 1.8; color: #1D2129; font-size: 14px;',
  h1: 'margin: 12px 0 8px; font-size: 20px; font-weight: 600; color: #1D2129;',
  h2: 'margin: 12px 0 8px; font-size: 18px; font-weight: 600; color: #1D2129;',
  h3: 'margin: 12px 0 8px; font-size: 16px; font-weight: 600; color: #1D2129;',
  img: 'max-width: 100%; border-radius: 6px;',
  a: 'color: #1677FF;',
  ul: 'padding-left: 22px;',
  ol: 'padding-left: 22px;',
  table: 'width: 100%; border-collapse: collapse; font-size: 13px;',
  th: 'background:#F7F8FA; font-weight: 600; border: 1px solid #F0F0F0; padding: 6px;',
  td: 'border: 1px solid #F0F0F0; padding: 6px;'
} as Record<string, string>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getTenderNoticeDetail } from '@/api/notice'

const type = ref<string>('announcement')
const id = ref<string | number | undefined>(undefined)
const detail = ref<Record<string, any>>({})
const attachments = ref<Array<{ fileName: string; filePath: string }>>([])
const domain = import.meta.env.VITE_APP_BASE_URL

const contentHtml = computed(() => {
  return detail.value.noticeContent || detail.value.publicityContent || detail.value.content || ''
})

// 公司/部门名称 - 支持多种字段名
const companyName = computed(() => {
  return detail.value.deptName || detail.value.companyName || detail.value.dept || detail.value.company || ''
})

// 发布时间 - 支持多种字段名
const publishTime = computed(() => {
  return detail.value.publishTime || detail.value.createTime || detail.value.time || detail.value.date || ''
})

function getTagClass(tab: string) {
  const map: Record<string, string> = {
    announcement: 'inquiry',
    result: 'tender',
    notice: 'negotiation',
    change: 'recruitment'
  }
  return map[tab] || 'inquiry'
}

function getTagText(tab: string) {
  const map: Record<string, string> = {
    announcement: '公告/邀请',
    result: '中标公示',
    notice: '中标公告',
    change: '招募公告'
  }
  return map[tab] || '公告'
}

function formatTime(timeStr: string) {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${year}/${month}/${day} ${hours}:${minutes}`
  } catch (e) {
    return timeStr
  }
}

function openAttachment(file: { fileName: string; filePath: string }) {
  const url = file.filePath
  if (!url) return
  // H5/小程序统一用内置 webview 打开
  uni.navigateTo({
    url: `/pages/webview/webview?url=${encodeURIComponent(url)}`
  })
}

function viewAttachments() {
  if (attachments.value.length === 0) {
    uni.showToast({
      title: '暂无附件',
      icon: 'none'
    })
    return
  }
  // 如果只有一个附件，直接打开
  if (attachments.value.length === 1) {
    openAttachment(attachments.value[0])
    return
  }
  // 多个附件时显示选择列表
  const itemList = attachments.value.map(f => f.fileName)
  uni.showActionSheet({
    itemList,
    success: (res) => {
      openAttachment(attachments.value[res.tapIndex])
    }
  })
}

function goHome() {
  uni.reLaunch({
    url: '/pages/index/index'
  })
}

async function fetchDetailIfNeeded() {
  if (type.value !== 'announcement' || !id.value) return
  try {
    const { data } = await getTenderNoticeDetail(id.value)
    if (data) {
      // 仅使用富文本与附件覆盖，其他字段保留列表传入的 detail
      detail.value = { ...detail.value, ...data }
      attachments.value = Array.isArray(data.attachmentInfos) ? data.attachmentInfos : []
    }
  } catch (e) {
    console.error('获取公告详情失败', e)
  }
}

onLoad((options: any) => {
  type.value = options?.type || 'announcement'
  id.value = options?.id

  // 从URL参数中获取基本信息
  if (options?.title || options?.deptName || options?.publishTime) {
    detail.value = {
      noticeTitle: decodeURIComponent(options.title || ''),
      publicityTitle: decodeURIComponent(options.title || ''),
      deptName: decodeURIComponent(options.deptName || ''),
      publishTime: decodeURIComponent(options.publishTime || ''),
      id: options.id
    }
  }

  // 拉取详情内容和附件
  fetchDetailIfNeeded()
})
</script>

<style lang="scss" scoped>
.announcement-detail-page {
  min-height: 100vh;
  background: #f0f2f5;
  padding-bottom: 80px; // 为底部按钮留出空间
}

.container {
  padding: 12px;
}

// 公告卡片
.announcement-card {
  background: #ffffff;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
}

.header {
  margin-bottom: 4px;
}

.title-row {
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

.tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  padding: 0 4px;
  border-radius: 2px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;

  &.inquiry {
    background: #cbeffc;
    color: #1d1d1d;
  }
  &.tender {
    background: #feeaf5;
    color: #c4277e;
  }
  &.negotiation {
    background: #e8f3f5;
    color: #126a7a;
  }
  &.recruitment {
    background: #fff7e6;
    color: #d46b08;
  }
}

.title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  line-height: 1.5;
  margin-left: 4px;
}

.meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.company {
  background: #f0f2f5;
  color: #4e5969;
  padding: 0 4px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 20px;
  flex-shrink: 0;
}

.time {
  color: #86909c;
  font-size: 12px;
  line-height: 20px;
}

// 内容卡片
.content-card {
  background: #ffffff;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
}

.attachments {
  background: #fff;
  border-radius: 6px;
  padding: 8px 8px 0 8px;
  .attach-title {
    font-size: 14px;
    color: #1d2129;
    font-weight: 500;
    padding: 8px;
  }
  .attach-list {
    .attach-item {
      display: flex;
      align-items: center;
      padding: 10px 8px;
      border-top: 1px solid #f0f0f0;
      .name {
        flex: 1;
        margin: 0 6px;
        color: #1677ff;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.empty {
  margin-top: 24px;
  color: #999;
  text-align: center;
}

// 底部按钮
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 8px 16px;
  background: #ffffff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.btn {
  flex: 1;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 32px;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s;

  &.btn-outline {
    background: #ffffff;
    color: #505762;
    border: 1px solid #cdd2da;

    &:active {
      background: #f5f7fa;
    }
  }

  &.btn-primary {
    background: #ffffff;
    color: #0069ff;
    border: 1px solid #0069ff;

    &:active {
      background: #f0f8ff;
    }
  }
}
</style>
